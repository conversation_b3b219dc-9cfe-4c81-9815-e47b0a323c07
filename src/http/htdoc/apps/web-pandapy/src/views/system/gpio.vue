<template>
  <Page
    description="本页面包含GPIO相关设定"
    title="I/O 接口和触发"
  >

  <NCard title="GPIO功能绑定"><GPIOFuncForm/></NCard><br/>

  </Page>
</template>

<script lang="ts" setup>
import { Page } from '@vben/common-ui';
import { NButton, NCard, useMessage } from 'naive-ui';
import { useVbenForm } from '#/adapter/form';
import { ref, onMounted } from 'vue';
import { getGPIOBinding, setGPIOBinding } from '#/api/index';

const message = useMessage();
const [GPIOFuncForm, formApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  handleSubmit: (values) => {

    setGPIOBinding(values).then(() => {
      message.success('设置已保存');
    }).catch((error) => {
      message.error(`保存失败: ${error.message}`);
    });

  },
  resetButtonOptions: {
    show: false,
  },
  schema: [
    {
      component: "InputNumber",
      fieldName: 'cameraTriggerGpio',
      label: '相机快门 IO',
    }
  ],
});

onMounted(() => {

  getGPIOBinding().then(res=>{
    console.log(res);
    formApi.setValues({
      cameraTriggerGpio: res.cameraTriggerGpio,
    });
  });

});
</script>
