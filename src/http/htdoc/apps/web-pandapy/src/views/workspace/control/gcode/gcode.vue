<template>
  <gcode-container class="">
    <ul>
      <li v-for="(command, index) in gcodeCommands" :key="index">
        <!-- 将指令逐字渲染 -->
        <span v-for="(char, charIndex) in command.split(' ')" :key="charIndex" :class="getCharClass(char)">
          {{ char }}
        </span>
      </li>
    </ul>
  </gcode-container>
  <gcode-input-container>
    <input type="text" placeholder="发送 G-code 指令"/>
    <div><span>发送</span></div>
  </gcode-input-container>
  <!-- <gcode-collpase-container>
    <span>点击收起</span>
  </gcode-collpase-container> -->
</template>
<style lang="scss" scoped>

gcode-container {
  display: flex;
  flex-direction: column;
  height: 40%;
  overflow-y: scroll;

  li {
    list-style: none;
    display: flex;
    align-items: center;
    padding: 0.5rem 0.5rem;

    border-bottom: 1px solid hsl(var(--border));
    background-color: hsl(var(--card));

    &:hover {
      background-color: hsl(var(--card-hover));
      cursor: pointer;
    }

    &:before {
      content: '<TX';
      margin-right: 1.5rem;
      color: hsl(var(--text));
    }

    &:after {
      content: '19:00 PM';
      font-size: 0.8rem;
      margin-left: auto;
      opacity: 0.3;
      color: hsl(var(--text));
    }

    span {
      font-family: 'Courier New', Courier, monospace;
      display: inline-block;
      font-size: 1rem;
      transition: color 0.3s ease;

      &.command-char {
        color: hsl(var(--success));
        font-weight: bold;
        margin-right: 0.5rem;
      }

      &.command-char-args {
        color: hsl(var(--text));
        margin-right: 0.5rem;
        font-style: italic;
      }
    }
  }
}

gcode-input-container {
  display: flex;

  input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid hsl(var(--border));
    font-size: 1rem;
    font-family: 'Courier New', Courier, monospace;

    /* 去掉 hover 默认样式 */
    &:focus {
      outline: none;
      border-color: hsl(var(--border)); /* 保持边框颜色一致 */
      // background-color: transparent; /* 保持背景颜色一致 */
    }
  }

  div > span {
    width: 5rem;
    height: 100%;
    display: block;
    text-align: center;
    align-content: center;
    background-color: #0465d5;

    &:hover {
      background-color: #0354b5;
      cursor: pointer;
    }
  }
}

gcode-collpase-container {
  display: block;
  width: 100%;
  text-align: center;

  span {
    opacity: 0.4;
    color: hsl(var(--text));
    display: inline-block;
    padding: 0.5rem 1rem;
    color: hsl(var(--text));

    &:hover {
      opacity: 1;
      cursor: pointer;
      color: hsl(var(--text-hover));
    }
  }
}

</style>

<script lang="ts" setup>

import { ref } from 'vue';
import { NButton } from 'naive-ui';

const gcodeCommands = [
  'G0 X10 Y10 Z0.3',
  'M104 S200',
  'M109 S200',
  'M109 S200',
  'M109 S200',
  'M140 S60',
  'M190 S60'
];

// 根据字符类型返回样式类
const getCharClass = (char: string) => {
  if(char.match(/^[GgMmJj]/)) {
    return 'command-char';
  } else {
    return 'command-char-args';
  }
};

</script>
