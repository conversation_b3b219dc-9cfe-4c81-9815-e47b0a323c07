<template>
  <TresCanvas :clear-color="bkgColor" :alpha="true" :preserveDrawingBuffer="true">
    <TresPerspectiveCamera :position="[0, 0, currentZoom]" />
    <!-- 渲染时才对角度取模 -->
    <TresMesh :rotation="[displayRotationX, displayRotationY, displayRotationZ]">
      <TresBoxGeometry :args="[2, 1, 1]" />
      <TresMeshNormalMaterial />
    </TresMesh>
  </TresCanvas>
</template>

<script lang="ts" setup>
import { TresCanvas } from '@tresjs/core';
import { onUnmounted, ref, watch, computed } from 'vue';
import * as THREE from 'three';

const props = defineProps({
  bkgColor: { type: String, default: 'transparent' },
  zoom: { type: Number, default: 4.5 },
  animate: { type: Boolean, default: false },
});

const rotationX = ref(-1);
const rotationY = ref(0);
const rotationZ = ref(-0.8);
let animationFrameId: number | null = null;

const targetRotationX = ref(rotationX.value);
const targetRotationY = ref(rotationY.value);
const targetRotationZ = ref(rotationZ.value);

const currentZoom = ref(props.zoom);
const targetZoom = ref(props.zoom);

const lerpFactor = 0.05;
const rotationSpeed = 0.01;

// 2π 取模函数
const TWO_PI = Math.PI * 2;
const modulo = (v: number) => ((v % TWO_PI) + TWO_PI) % TWO_PI;

// 渲染时取模
const displayRotationX = computed(() => modulo(rotationX.value));
const displayRotationY = computed(() => modulo(rotationY.value));
const displayRotationZ = computed(() => modulo(rotationZ.value));

const animateFrame = () => {
  if (props.animate) {
    // 只移动目标角度，不取模
    targetRotationX.value += rotationSpeed;
    targetRotationY.value += rotationSpeed;
    targetRotationZ.value += rotationSpeed;
    targetZoom.value = props.zoom;
  }

  // 对“无限制”角度做插值
  rotationX.value = THREE.MathUtils.lerp(rotationX.value, targetRotationX.value, lerpFactor);
  rotationY.value = THREE.MathUtils.lerp(rotationY.value, targetRotationY.value, lerpFactor);
  rotationZ.value = THREE.MathUtils.lerp(rotationZ.value, targetRotationZ.value, lerpFactor);
  currentZoom.value = THREE.MathUtils.lerp(currentZoom.value, targetZoom.value, lerpFactor);

  animationFrameId = requestAnimationFrame(animateFrame);
};

const startAnimation = () => {
  stopAnimation();
  animationFrameId = requestAnimationFrame(animateFrame);
};

const stopAnimation = () => {
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
};

watch(
  () => props.animate,
  (run) => {
    if (run) {
      // 开始旋转前，把当前角度作为目标基准
      targetRotationX.value = rotationX.value;
      targetRotationY.value = rotationY.value;
      targetRotationZ.value = rotationZ.value;
      targetZoom.value = props.zoom;
      startAnimation();
    } else {
      // 停止时平滑回到初始姿态，选取最短旋转路径
      const defaultX = modulo(-1)
      const defaultY = modulo(0)
      const defaultZ = modulo(-0.8)
      const curX = modulo(rotationX.value)
      const curY = modulo(rotationY.value)
      const curZ = modulo(rotationZ.value)
      let deltaX = defaultX - curX
      let deltaY = defaultY - curY
      let deltaZ = defaultZ - curZ
      if (deltaX > Math.PI) deltaX -= TWO_PI
      if (deltaX < -Math.PI) deltaX += TWO_PI
      if (deltaY > Math.PI) deltaY -= TWO_PI
      if (deltaY < -Math.PI) deltaY += TWO_PI
      if (deltaZ > Math.PI) deltaZ -= TWO_PI
      if (deltaZ < -Math.PI) deltaZ += TWO_PI
      targetRotationX.value = rotationX.value + deltaX
      targetRotationY.value = rotationY.value + deltaY
      targetRotationZ.value = rotationZ.value + deltaZ
      targetZoom.value = props.zoom
      startAnimation()
    }
  },
  { immediate: true }
);

onUnmounted(stopAnimation);
</script>
